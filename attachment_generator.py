#!/usr/bin/env python3
"""
Attachment Generator Script

This script connects to a PostgreSQL database and:
1. Retrieves tables with specific column types from metadata
2. Counts active records in each table
3. Filters tables with at least 10 active records
4. Prints the results
"""

import psycopg2
import sys
import os
from typing import List, Tuple, Dict

def load_database_config(properties_file: str = "database.properties") -> Dict[str, str]:
    """
    Load database configuration from properties file.
    
    Args:
        properties_file: Path to the properties file
        
    Returns:
        Dict[str, str]: Dictionary containing database configuration
        
    Raises:
        FileNotFoundError: If properties file doesn't exist
        ValueError: If required properties are missing
    """
    if not os.path.exists(properties_file):
        raise FileNotFoundError(f"Properties file '{properties_file}' not found. Please create it with your database credentials.")
    
    config = {}
    required_keys = ['db.host', 'db.database', 'db.user', 'db.password', 'db.port']
    
    try:
        with open(properties_file, 'r') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                # Skip empty lines and comments
                if not line or line.startswith('#'):
                    continue
                
                if '=' not in line:
                    print(f"Warning: Invalid line {line_num} in {properties_file}: {line}")
                    continue
                
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()
                config[key] = value
        
        # Check for required keys
        missing_keys = [key for key in required_keys if key not in config]
        if missing_keys:
            raise ValueError(f"Missing required properties: {', '.join(missing_keys)}")
        
        # Convert to psycopg2 format
        db_config = {
            'host': config['db.host'],
            'database': config['db.database'],
            'user': config['db.user'],
            'password': config['db.password'],
            'port': int(config['db.port'])
        }
        
        # Add optional timeout if specified
        if 'db.timeout' in config:
            db_config['connect_timeout'] = int(config['db.timeout'])
        
        return db_config
        
    except Exception as e:
        raise ValueError(f"Error reading properties file '{properties_file}': {e}")

def connect_to_database() -> psycopg2.extensions.connection:
    """
    Establish connection to PostgreSQL database using configuration from properties file.
    
    Returns:
        psycopg2.extensions.connection: Database connection object
        
    Raises:
        psycopg2.Error: If connection fails
    """
    try:
        # Load database configuration
        db_config = load_database_config()
        
        # Connect to database
        connection = psycopg2.connect(**db_config)
        print("Successfully connected to PostgreSQL database")
        return connection
    except (FileNotFoundError, ValueError) as e:
        print(f"Configuration error: {e}")
        sys.exit(1)
    except psycopg2.Error as e:
        print(f"Error connecting to PostgreSQL database: {e}")
        sys.exit(1)

def get_tables_with_column_type_32(cursor) -> Tuple[List[str], Dict[str, List[str]]]:
    """
    Execute query to get tables with column type 32.

    Args:
        cursor: Database cursor object

    Returns:
        tuple: (List of distinct table names, Dictionary mapping table names to their column names)
    """
    query = """
    SELECT columnname, typename AS tablename
    FROM columnmetadata c
    WHERE objtype = 'Table' AND columntype IN (32);
    """

    try:
        cursor.execute(query)
        results = cursor.fetchall()

        # Store results in dictionary: table_name -> list of column names
        table_columns = {}
        for column_name, table_name in results:
            if table_name not in table_columns:
                table_columns[table_name] = []
            table_columns[table_name].append(column_name)

        # Get distinct table names
        table_names = list(table_columns.keys())

        print(f"Found {len(table_names)} tables with column type 32")
        print(f"Total columns with type 32: {len(results)}")

        return table_names, table_columns
    except psycopg2.Error as e:
        print(f"Error executing metadata query: {e}")
        return [], {}

def get_active_record_count(cursor, table_name: str) -> int:
    """
    Get count of active records for a specific table.
    
    Args:
        cursor: Database cursor object
        table_name: Name of the table to count records from
        
    Returns:
        int: Number of active records, or 0 if error occurs
    """
    # Using parameterized query with psycopg2.sql for safe table name handling
    from psycopg2 import sql
    
    query = sql.SQL("""
    SELECT COUNT(id) 
    FROM custom_registers.{table} 
    WHERE originalid IS NULL;
    """).format(table=sql.Identifier(table_name))
    
    try:
        cursor.execute(query)
        result = cursor.fetchone()
        count = result[0] if result else 0
        print(f"Table '{table_name}': {count} active records")
        return count
    except psycopg2.Error as e:
        print(f"Error counting records for table '{table_name}': {e}")
        return 0

def analyze_tables() -> tuple[Dict[str, int], Dict[str, List[str]]]:
    """
    Main function to analyze tables and return filtered results.

    Returns:
        tuple: (Dictionary of table names and their record counts (only tables with >= 10 records),
                Dictionary mapping table names to their column names)
    """
    connection = None
    cursor = None

    try:
        # Connect to database
        connection = connect_to_database()
        cursor = connection.cursor()

        # Get tables with column type 32 and their columns
        table_names, table_columns = get_tables_with_column_type_32(cursor)

        if not table_names:
            print("No tables found with column type 32")
            return {}, {}

        # Get record counts for each table
        table_counts = {}
        print("\nCounting active records for each table...")

        for table_name in table_names:
            count = get_active_record_count(cursor, table_name)
            table_counts[table_name] = count

        # Filter tables with at least 10 active records
        filtered_tables = {
            table: count for table, count in table_counts.items()
            if count >= 10
        }

        return filtered_tables, table_columns

    except Exception as e:
        print(f"Unexpected error during analysis: {e}")
        return {}, {}

    finally:
        # Clean up database connections
        if cursor:
            cursor.close()
        if connection:
            connection.close()
            print("\nDatabase connection closed")

def print_results(filtered_tables: Dict[str, int]) -> None:
    """
    Print the final results in a formatted way.
    
    Args:
        filtered_tables: Dictionary of table names and record counts
    """
    print("\n" + "="*60)
    print("TABLES WITH AT LEAST 10 ACTIVE RECORDS")
    print("="*60)
    
    if not filtered_tables:
        print("No tables found with 10 or more active records.")
        return
    
    # Sort tables by record count (descending)
    sorted_tables = sorted(filtered_tables.items(), key=lambda x: x[1], reverse=True)
    
    print(f"{'Table Name':<30} {'Active Records':<15}")
    print("-" * 45)
    
    for table_name, count in sorted_tables:
        print(f"{table_name:<30} {count:<15,}")
    
    print(f"\nTotal tables with >= 10 records: {len(filtered_tables)}")

def main():
    """Main execution function."""
    print("Attachment Generator")
    print("=" * 20)

    # Analyze tables and get filtered results
    filtered_tables, table_columns = analyze_tables()

    # Print results
    print_results(filtered_tables)

    # Store table_columns for future use (currently just available in scope)
    # This dictionary contains the mapping of table names to their column names

if __name__ == "__main__":
    main()
