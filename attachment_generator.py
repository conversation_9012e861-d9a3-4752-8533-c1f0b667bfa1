#!/usr/bin/env python3
"""
Attachment Generator Script

This script connects to a PostgreSQL database and:
1. Retrieves tables with specific column types from metadata
2. Counts active records in each table
3. Filters tables with at least 10 active records
4. Prints the results
"""

import psycopg2
import sys
import os
import uuid
import random
from datetime import datetime
from typing import List, Tuple, Dict

def load_database_config(properties_file: str = "database.properties") -> Dict[str, str]:
    """
    Load database configuration from properties file.
    
    Args:
        properties_file: Path to the properties file
        
    Returns:
        Dict[str, str]: Dictionary containing database configuration
        
    Raises:
        FileNotFoundError: If properties file doesn't exist
        ValueError: If required properties are missing
    """
    if not os.path.exists(properties_file):
        raise FileNotFoundError(f"Properties file '{properties_file}' not found. Please create it with your database credentials.")
    
    config = {}
    required_keys = ['db.host', 'db.database', 'db.user', 'db.password', 'db.port']
    
    try:
        with open(properties_file, 'r') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                # Skip empty lines and comments
                if not line or line.startswith('#'):
                    continue
                
                if '=' not in line:
                    print(f"Warning: Invalid line {line_num} in {properties_file}: {line}")
                    continue
                
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()
                config[key] = value
        
        # Check for required keys
        missing_keys = [key for key in required_keys if key not in config]
        if missing_keys:
            raise ValueError(f"Missing required properties: {', '.join(missing_keys)}")
        
        # Convert to psycopg2 format
        db_config = {
            'host': config['db.host'],
            'database': config['db.database'],
            'user': config['db.user'],
            'password': config['db.password'],
            'port': int(config['db.port'])
        }
        
        # Add optional timeout if specified
        if 'db.timeout' in config:
            db_config['connect_timeout'] = int(config['db.timeout'])
        
        return db_config
        
    except Exception as e:
        raise ValueError(f"Error reading properties file '{properties_file}': {e}")

def connect_to_database() -> psycopg2.extensions.connection:
    """
    Establish connection to PostgreSQL database using configuration from properties file.
    
    Returns:
        psycopg2.extensions.connection: Database connection object
        
    Raises:
        psycopg2.Error: If connection fails
    """
    try:
        # Load database configuration
        db_config = load_database_config()
        
        # Connect to database
        connection = psycopg2.connect(**db_config)
        print("Successfully connected to PostgreSQL database")
        return connection
    except (FileNotFoundError, ValueError) as e:
        print(f"Configuration error: {e}")
        sys.exit(1)
    except psycopg2.Error as e:
        print(f"Error connecting to PostgreSQL database: {e}")
        sys.exit(1)

def get_tables_with_column_type_32(cursor) -> Tuple[List[str], Dict[str, List[str]]]:
    """
    Execute query to get tables with column type 32.

    Args:
        cursor: Database cursor object

    Returns:
        tuple: (List of distinct table names, Dictionary mapping table names to their column names)
    """
    query = """
    SELECT columnname, typename AS tablename
    FROM columnmetadata c
    WHERE objtype = 'Table' AND columntype IN (32);
    """

    try:
        cursor.execute(query)
        results = cursor.fetchall()

        # Store results in dictionary: table_name -> list of column names
        table_columns = {}
        for column_name, table_name in results:
            if table_name not in table_columns:
                table_columns[table_name] = []
            table_columns[table_name].append(column_name)

        # Get distinct table names
        table_names = list(table_columns.keys())

        print(f"Found {len(table_names)} tables with column type 32")
        print(f"Total columns with type 32: {len(results)}")

        return table_names, table_columns
    except psycopg2.Error as e:
        print(f"Error executing metadata query: {e}")
        return [], {}

def get_active_record_count(cursor, table_name: str) -> int:
    """
    Get count of active records for a specific table.
    
    Args:
        cursor: Database cursor object
        table_name: Name of the table to count records from
        
    Returns:
        int: Number of active records, or 0 if error occurs
    """
    # Using parameterized query with psycopg2.sql for safe table name handling
    from psycopg2 import sql
    
    query = sql.SQL("""
    SELECT COUNT(id) 
    FROM custom_registers.{table} 
    WHERE originalid IS NULL;
    """).format(table=sql.Identifier(table_name))
    
    try:
        cursor.execute(query)
        result = cursor.fetchone()
        count = result[0] if result else 0
        print(f"Table '{table_name}': {count} active records")
        return count
    except psycopg2.Error as e:
        print(f"Error counting records for table '{table_name}': {e}")
        return 0

def analyze_tables() -> Tuple[Dict[str, int], Dict[str, List[str]]]:
    """
    Main function to analyze tables and return filtered results.

    Returns:
        tuple: (Dictionary of table names and their record counts (only tables with >= 10 records),
                Dictionary mapping table names to their column names)
    """
    connection = None
    cursor = None

    try:
        # Connect to database
        connection = connect_to_database()
        cursor = connection.cursor()

        # Get tables with column type 32 and their columns
        table_names, table_columns = get_tables_with_column_type_32(cursor)

        if not table_names:
            print("No tables found with column type 32")
            return {}, {}

        # Get record counts for each table
        table_counts = {}
        print("\nCounting active records for each table...")

        for table_name in table_names:
            count = get_active_record_count(cursor, table_name)
            table_counts[table_name] = count

        # Filter tables with at least 10 active records
        filtered_tables = {
            table: count for table, count in table_counts.items()
            if count >= 10
        }

        # Filter table_columns to only include tables with >= 10 records
        filtered_table_columns = {
            table: columns for table, columns in table_columns.items()
            if table in filtered_tables
        }

        return filtered_tables, filtered_table_columns

    except Exception as e:
        print(f"Unexpected error during analysis: {e}")
        return {}, {}

    finally:
        # Clean up database connections
        if cursor:
            cursor.close()
        if connection:
            connection.close()
            print("\nDatabase connection closed")

# Global variables for sequence caching
_attachment_seq_cache = []
_attachmentlink_seq_cache = []

def get_next_attachment_id(cursor) -> int:
    """
    Get next ID from attachment sequence with local caching.

    Args:
        cursor: Database cursor object

    Returns:
        int: Next available attachment ID
    """
    global _attachment_seq_cache

    if not _attachment_seq_cache:
        # Cache next 20 IDs from sequence
        try:
            cursor.execute("SELECT nextval('public.pkseq_attachment') FROM generate_series(1, 20)")
            _attachment_seq_cache = [row[0] for row in cursor.fetchall()]
        except psycopg2.Error as e:
            print(f"Error getting attachment sequence values: {e}")
            raise

    return _attachment_seq_cache.pop(0)

def get_next_attachmentlink_id(cursor) -> int:
    """
    Get next ID from attachmentlink sequence with local caching.

    Args:
        cursor: Database cursor object

    Returns:
        int: Next available attachmentlink ID
    """
    global _attachmentlink_seq_cache

    if not _attachmentlink_seq_cache:
        # Cache next 20 IDs from sequence
        try:
            cursor.execute("SELECT nextval('public.pkseq_attachmentlink') FROM generate_series(1, 20)")
            _attachmentlink_seq_cache = [row[0] for row in cursor.fetchall()]
        except psycopg2.Error as e:
            print(f"Error getting attachmentlink sequence values: {e}")
            raise

    return _attachmentlink_seq_cache.pop(0)

def get_random_attachment_file() -> Tuple[str, bytes, int]:
    """
    Get a random file from the attachments folder.

    Returns:
        Tuple[str, bytes, int]: (filename, file_content, file_size)
    """
    attachments_dir = "attachments"

    if not os.path.exists(attachments_dir):
        raise FileNotFoundError(f"Attachments directory '{attachments_dir}' not found")

    # Get all .txt files from attachments directory
    files = [f for f in os.listdir(attachments_dir) if f.endswith('.txt')]

    if not files:
        raise FileNotFoundError(f"No .txt files found in '{attachments_dir}' directory")

    # Select random file
    filename = random.choice(files)
    filepath = os.path.join(attachments_dir, filename)

    # Read file content
    try:
        with open(filepath, 'rb') as f:
            content = f.read()

        file_size = len(content)
        return filename, content, file_size

    except Exception as e:
        print(f"Error reading file '{filepath}': {e}")
        raise

def insert_attachment_record(cursor, attachment_id: int, file_content: bytes, filename: str,
                           table_name: str, record_id: int, column_name: str, file_size: int) -> None:
    """
    Insert a record into the attachment table.

    Args:
        cursor: Database cursor object
        attachment_id: ID for the attachment record
        file_content: Binary content of the file
        filename: Name of the file
        table_name: Name of the linked table
        record_id: ID of the linked record
        column_name: Name of the linked column
        file_size: Size of the file in bytes
    """
    current_time = datetime.now()
    created_by = 2220
    uuid_str = str(uuid.uuid4())

    insert_query = """
    INSERT INTO public.attachment (
        id, file, filename, createdate, createdby, lastmodifieddate,
        lastmodifiedby, linktable, linkid, linkcolumn, isactive, filesize, uuid
    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """

    try:
        cursor.execute(insert_query, (
            attachment_id, file_content, filename, current_time, created_by,
            current_time, created_by, table_name, record_id, column_name,
            False, file_size, uuid_str
        ))
    except psycopg2.Error as e:
        print(f"Error inserting attachment record: {e}")
        raise

def insert_attachmentlink_record(cursor, link_id: int, attachment_id: int,
                                record_id: int, table_name: str, column_name: str) -> None:
    """
    Insert a record into the attachmentlink table.

    Args:
        cursor: Database cursor object
        link_id: ID for the attachmentlink record
        attachment_id: ID of the related attachment record
        record_id: ID of the linked record
        table_name: Name of the linked table
        column_name: Name of the linked column
    """
    insert_query = """
    INSERT INTO public.attachmentlink (
        id, attachmentid, linkid, linktable, linkcolumn
    ) VALUES (%s, %s, %s, %s, %s)
    """

    try:
        cursor.execute(insert_query, (
            link_id, attachment_id, record_id, table_name, column_name
        ))
    except psycopg2.Error as e:
        print(f"Error inserting attachmentlink record: {e}")
        raise

def print_results(filtered_tables: Dict[str, int]) -> None:
    """
    Print the final results in a formatted way.

    Args:
        filtered_tables: Dictionary of table names and record counts
    """
    print("\n" + "="*60)
    print("TABLES WITH AT LEAST 10 ACTIVE RECORDS")
    print("="*60)

    if not filtered_tables:
        print("No tables found with 10 or more active records.")
        return

    # Sort tables by record count (descending)
    sorted_tables = sorted(filtered_tables.items(), key=lambda x: x[1], reverse=True)

    print(f"{'Table Name':<30} {'Active Records':<15}")
    print("-" * 45)

    for table_name, count in sorted_tables:
        print(f"{table_name:<30} {count:<15,}")

    print(f"\nTotal tables with >= 10 records: {len(filtered_tables)}")

def get_active_record_ids(cursor, table_name: str) -> List[int]:
    """
    Get active record IDs for a specific table.

    Args:
        cursor: Database cursor object
        table_name: Name of the table to query

    Returns:
        List[int]: List of active record IDs
    """
    from psycopg2 import sql

    query = sql.SQL("""
    SELECT id FROM custom_registers.{table} WHERE originalid IS NULL;
    """).format(table=sql.Identifier(table_name))

    try:
        cursor.execute(query)
        results = cursor.fetchall()
        record_ids = [row[0] for row in results]
        return record_ids
    except psycopg2.Error as e:
        print(f"Error getting active record IDs for table '{table_name}': {e}")
        return []

def generate_attachments(filtered_table_columns: Dict[str, List[str]]) -> None:
    """
    Generate attachment records for all tables and their columns.

    Args:
        filtered_table_columns: Dictionary mapping table names to their column names
    """
    connection = None
    cursor = None

    try:
        # Connect to database
        connection = connect_to_database()
        cursor = connection.cursor()

        total_attachments = 0

        print("\n" + "="*60)
        print("GENERATING ATTACHMENTS")
        print("="*60)

        for table_name, column_names in filtered_table_columns.items():
            print(f"\nProcessing table: {table_name}")

            # Get active record IDs for this table
            record_ids = get_active_record_ids(cursor, table_name)

            if not record_ids:
                print(f"  No active records found for table '{table_name}'")
                continue

            print(f"  Found {len(record_ids)} active records")
            print(f"  Columns to process: {', '.join(column_names)}")

            # Generate attachments for each record and each column
            table_attachment_count = 0

            for record_id in record_ids:
                for column_name in column_names:
                    try:
                        # Get random file
                        filename, file_content, file_size = get_random_attachment_file()

                        # Get next IDs
                        attachment_id = get_next_attachment_id(cursor)
                        link_id = get_next_attachmentlink_id(cursor)

                        # Insert attachment record
                        insert_attachment_record(
                            cursor, attachment_id, file_content, filename,
                            table_name, record_id, column_name, file_size
                        )

                        # Insert attachmentlink record
                        insert_attachmentlink_record(
                            cursor, link_id, attachment_id, record_id,
                            table_name, column_name
                        )

                        table_attachment_count += 1
                        total_attachments += 1

                        # Commit every 100 records for better performance
                        if total_attachments % 100 == 0:
                            connection.commit()
                            print(f"  Committed {total_attachments} attachments so far...")

                    except Exception as e:
                        print(f"  Error creating attachment for record {record_id}, column {column_name}: {e}")
                        connection.rollback()
                        continue

            print(f"  Created {table_attachment_count} attachments for table '{table_name}'")

        # Final commit
        connection.commit()

        print(f"\n" + "="*60)
        print(f"ATTACHMENT GENERATION COMPLETED")
        print(f"Total attachments created: {total_attachments}")
        print("="*60)

    except Exception as e:
        print(f"Error during attachment generation: {e}")
        if connection:
            connection.rollback()

    finally:
        # Clean up database connections
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def main():
    """Main execution function."""
    print("Attachment Generator")
    print("=" * 20)

    # Analyze tables and get filtered results
    filtered_tables, filtered_table_columns = analyze_tables()

    # Print results
    print_results(filtered_tables)

    # Print column information for filtered tables
    if filtered_table_columns:
        print(f"\nColumn information available for {len(filtered_table_columns)} qualifying tables")

    # Store filtered_table_columns for future use (currently just available in scope)
    # This dictionary contains the mapping of table names to their column names
    # Only includes tables with 10 or more active records

if __name__ == "__main__":
    main()
