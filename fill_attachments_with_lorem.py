import os
import random
from faker import Faker

ATTACHMENTS_DIR = 'attachments'

fake = Faker()


def fill_files_with_lorem(directory: str):
    for filename in os.listdir(directory):
        if filename.endswith('.txt'):
            file_path = os.path.join(directory, filename)
            num_paragraphs = random.randint(3, 5)
            paragraphs = fake.paragraphs(nb=num_paragraphs)
            content = '\n\n'.join(paragraphs)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Filled {filename} with {num_paragraphs} paragraphs of Lorem Ipsum.")


def main():
    fill_files_with_lorem(ATTACHMENTS_DIR)


if __name__ == '__main__':
    main() 