#!/usr/bin/env python3
"""
PostgreSQL Table Analyzer Script

This script connects to a PostgreSQL database and:
1. Retrieves tables with specific column types from metadata
2. Counts active records in each table
3. Filters tables with at least 10 active records
4. Prints the results
"""

import psycopg2
import sys
from typing import List, Tuple, Dict

# Database connection settings - UPDATE THESE WITH YOUR ACTUAL CREDENTIALS
DB_CONFIG = {
    'host': 'localhost',        # Replace with your PostgreSQL host
    'database': 'your_db_name', # Replace with your database name
    'user': 'your_username',    # Replace with your username
    'password': 'your_password', # Replace with your password
    'port': 5432               # Replace with your port if different
}

def connect_to_database() -> psycopg2.extensions.connection:
    """
    Establish connection to PostgreSQL database.
    
    Returns:
        psycopg2.extensions.connection: Database connection object
        
    Raises:
        psycopg2.Error: If connection fails
    """
    try:
        connection = psycopg2.connect(**DB_CONFIG)
        print("Successfully connected to PostgreSQL database")
        return connection
    except psycopg2.Error as e:
        print(f"Error connecting to PostgreSQL database: {e}")
        sys.exit(1)

def get_tables_with_column_type_32(cursor) -> List[str]:
    """
    Execute query to get tables with column type 32.
    
    Args:
        cursor: Database cursor object
        
    Returns:
        List[str]: List of distinct table names
    """
    query = """
    SELECT DISTINCT typename AS tablename 
    FROM columnmetadata c 
    WHERE objtype = 'Table' AND columntype IN (32);
    """
    
    try:
        cursor.execute(query)
        results = cursor.fetchall()
        table_names = [row[0] for row in results]
        print(f"Found {len(table_names)} tables with column type 32")
        return table_names
    except psycopg2.Error as e:
        print(f"Error executing metadata query: {e}")
        return []

def get_active_record_count(cursor, table_name: str) -> int:
    """
    Get count of active records for a specific table.
    
    Args:
        cursor: Database cursor object
        table_name: Name of the table to count records from
        
    Returns:
        int: Number of active records, or 0 if error occurs
    """
    # Using parameterized query with psycopg2.sql for safe table name handling
    from psycopg2 import sql
    
    query = sql.SQL("""
    SELECT COUNT(id) 
    FROM custom_registers.{table} 
    WHERE originalid IS NULL;
    """).format(table=sql.Identifier(table_name))
    
    try:
        cursor.execute(query)
        result = cursor.fetchone()
        count = result[0] if result else 0
        print(f"Table '{table_name}': {count} active records")
        return count
    except psycopg2.Error as e:
        print(f"Error counting records for table '{table_name}': {e}")
        return 0

def analyze_tables() -> Dict[str, int]:
    """
    Main function to analyze tables and return filtered results.
    
    Returns:
        Dict[str, int]: Dictionary of table names and their record counts
                       (only tables with >= 10 records)
    """
    connection = None
    cursor = None
    
    try:
        # Connect to database
        connection = connect_to_database()
        cursor = connection.cursor()
        
        # Get tables with column type 32
        table_names = get_tables_with_column_type_32(cursor)
        
        if not table_names:
            print("No tables found with column type 32")
            return {}
        
        # Get record counts for each table
        table_counts = {}
        print("\nCounting active records for each table...")
        
        for table_name in table_names:
            count = get_active_record_count(cursor, table_name)
            table_counts[table_name] = count
        
        # Filter tables with at least 10 active records
        filtered_tables = {
            table: count for table, count in table_counts.items() 
            if count >= 10
        }
        
        return filtered_tables
        
    except Exception as e:
        print(f"Unexpected error during analysis: {e}")
        return {}
        
    finally:
        # Clean up database connections
        if cursor:
            cursor.close()
        if connection:
            connection.close()
            print("\nDatabase connection closed")

def print_results(filtered_tables: Dict[str, int]) -> None:
    """
    Print the final results in a formatted way.
    
    Args:
        filtered_tables: Dictionary of table names and record counts
    """
    print("\n" + "="*60)
    print("TABLES WITH AT LEAST 10 ACTIVE RECORDS")
    print("="*60)
    
    if not filtered_tables:
        print("No tables found with 10 or more active records.")
        return
    
    # Sort tables by record count (descending)
    sorted_tables = sorted(filtered_tables.items(), key=lambda x: x[1], reverse=True)
    
    print(f"{'Table Name':<30} {'Active Records':<15}")
    print("-" * 45)
    
    for table_name, count in sorted_tables:
        print(f"{table_name:<30} {count:<15,}")
    
    print(f"\nTotal tables with >= 10 records: {len(filtered_tables)}")

def main():
    """Main execution function."""
    print("PostgreSQL Table Analyzer")
    print("=" * 30)
    
    # Analyze tables and get filtered results
    filtered_tables = analyze_tables()
    
    # Print results
    print_results(filtered_tables)

if __name__ == "__main__":
    main()
