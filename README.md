# Attachment Generator

This Python script connects to a PostgreSQL database and analyzes tables based on specific criteria.

## What it does

1. **Queries metadata**: Retrieves tables that have columns with type 32 from the `columnmetadata` table
2. **Counts active records**: For each table, counts records where `originalid IS NULL` in the `custom_registers` schema
3. **Filters results**: Only shows tables with 10 or more active records
4. **Displays results**: Prints a formatted list of tables with their record counts

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Database Connection

Create and edit the `database.properties` file with your actual database credentials:

```properties
# Database Connection Properties
db.host=your_host
db.database=your_db_name
db.user=your_username
db.password=your_password
db.port=5432
db.timeout=30
```

**Important**: The `database.properties` file is required for the script to run. Copy the provided template and update it with your actual database credentials.

## Usage

Run the script:

```bash
python attachment_generator.py
```

## Expected Output

The script will output:
- Connection status
- Number of tables found with column type 32
- Progress as it counts records for each table
- Final formatted table showing:
  - Table names
  - Active record counts
  - Total number of qualifying tables

Example output:
```
Attachment Generator
====================
Successfully connected to PostgreSQL database
Found 15 tables with column type 32

Counting active records for each table...
Table 'users': 1250 active records
Table 'orders': 890 active records
Table 'products': 45 active records
...

============================================================
TABLES WITH AT LEAST 10 ACTIVE RECORDS
============================================================
Table Name                     Active Records
---------------------------------------------
users                          1,250
orders                         890
products                       45
categories                     23

Total tables with >= 10 records: 4
```

## Files

- **`attachment_generator.py`** - Main script file
- **`database.properties`** - Database configuration file (you need to create/edit this)
- **`requirements.txt`** - Python dependencies
- **`README.md`** - This documentation

## Requirements

- Python 3.6+
- PostgreSQL database with:
  - `columnmetadata` table
  - `custom_registers` schema with tables containing `id` and `originalid` columns
- Network access to the PostgreSQL server

## Error Handling

The script includes comprehensive error handling for:
- Database connection failures
- Missing or invalid properties file
- SQL execution errors
- Missing tables or columns
- Network issues

If any errors occur, they will be displayed with descriptive messages.

## Security Notes

- Keep your `database.properties` file secure and do not commit it to version control
- Consider adding `database.properties` to your `.gitignore` file
- Use environment variables or secure credential management in production environments
