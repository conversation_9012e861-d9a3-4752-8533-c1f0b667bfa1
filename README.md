# PostgreSQL Table Analyzer

This Python script connects to a PostgreSQL database and analyzes tables based on specific criteria.

## What it does

1. **Queries metadata**: Retrieves tables that have columns with type 32 from the `columnmetadata` table
2. **Counts active records**: For each table, counts records where `originalid IS NULL` in the `custom_registers` schema
3. **Filters results**: Only shows tables with 10 or more active records
4. **Displays results**: Prints a formatted list of tables with their record counts

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Database Connection

Edit the `DB_CONFIG` dictionary in `postgres_table_analyzer.py` with your actual database credentials:

```python
DB_CONFIG = {
    'host': 'your_host',        # e.g., 'localhost' or 'your-db-server.com'
    'database': 'your_db_name', # Your database name
    'user': 'your_username',    # Your PostgreSQL username
    'password': 'your_password', # Your PostgreSQL password
    'port': 5432               # PostgreSQL port (usually 5432)
}
```

## Usage

Run the script:

```bash
python postgres_table_analyzer.py
```

## Expected Output

The script will output:
- Connection status
- Number of tables found with column type 32
- Progress as it counts records for each table
- Final formatted table showing:
  - Table names
  - Active record counts
  - Total number of qualifying tables

Example output:
```
PostgreSQL Table Analyzer
==============================
Successfully connected to PostgreSQL database
Found 15 tables with column type 32

Counting active records for each table...
Table 'users': 1250 active records
Table 'orders': 890 active records
Table 'products': 45 active records
...

============================================================
TABLES WITH AT LEAST 10 ACTIVE RECORDS
============================================================
Table Name                     Active Records 
---------------------------------------------
users                          1,250          
orders                         890            
products                       45             
categories                     23             

Total tables with >= 10 records: 4
```

## Requirements

- Python 3.6+
- PostgreSQL database with:
  - `columnmetadata` table
  - `custom_registers` schema with tables containing `id` and `originalid` columns
- Network access to the PostgreSQL server

## Error Handling

The script includes comprehensive error handling for:
- Database connection failures
- SQL execution errors
- Missing tables or columns
- Network issues

If any errors occur, they will be displayed with descriptive messages.
